/**
 * Activation Engine - Handles panel activation via mouse hover and hotkeys
 * Migrated from C# SideView.UI.Services.ActivationEngine
 */

import { EventEmitter } from 'events';
import { screen, globalShortcut, BrowserWindow } from 'electron';
import { ConfigurationService } from '@modules/core/configuration-service';
import { PanelPosition } from '@shared/types/app.types';
import { ActivationSource, ActivationEventArgs } from '@shared/types/events.types';

export class ActivationEngine extends EventEmitter {
  private readonly configurationService: ConfigurationService;
  private readonly logger: Console;

  private isStarted = false;
  private mouseCheckInterval?: NodeJS.Timeout;
  private registeredHotkeys: string[] = [];
  private lastMousePosition = { x: 0, y: 0 };
  private activationTimer?: NodeJS.Timeout;
  private isInActivationZone = false;
  private isPreviewMode = false;
  private isPanelPinned = false;
  private activationIndicator?: BrowserWindow;
  private isMouseOverPanel = false;
  private panelBounds: { x: number; y: number; width: number; height: number } | null = null;
  private lastMouseCheckTime = 0;
  private mouseCheckDebounceMs = 50; // Debounce mouse checks to prevent rapid toggling

  constructor(
    configurationService: ConfigurationService,
    logger: Console = console
  ) {
    super();
    this.configurationService = configurationService;
    this.logger = logger;
  }

  async start(): Promise<void> {
    if (this.isStarted) {
      return;
    }

    this.logger.info('Starting Activation Engine');

    try {
      await this.registerHotkeys();
      this.startMouseTracking();
      
      this.isStarted = true;
      this.logger.info('Activation Engine started successfully');

    } catch (error) {
      this.logger.error('Failed to start Activation Engine:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isStarted) {
      return;
    }

    this.logger.info('Stopping Activation Engine');

    try {
      this.unregisterHotkeys();
      this.stopMouseTracking();
      this.hideActivationIndicator();

      this.isStarted = false;
      this.logger.info('Activation Engine stopped successfully');

    } catch (error) {
      this.logger.error('Error stopping Activation Engine:', error);
      throw error;
    }
  }

  private async registerHotkeys(): Promise<void> {
    const settings = this.configurationService.getSettings();
    
    if (!settings.hotkeys.enabled) {
      this.logger.debug('Hotkeys disabled in configuration');
      return;
    }

    try {
      // Register toggle panel hotkey
      if (settings.hotkeys.togglePanel) {
        const success = globalShortcut.register(settings.hotkeys.togglePanel, () => {
          this.onHotkeyPressed('togglePanel', settings.hotkeys.togglePanel);
        });

        if (success) {
          this.registeredHotkeys.push(settings.hotkeys.togglePanel);
          this.logger.debug(`Registered hotkey: ${settings.hotkeys.togglePanel}`);
        } else {
          this.logger.warn(`Failed to register hotkey: ${settings.hotkeys.togglePanel}`);
        }
      }

      // Register new tab hotkey
      if (settings.hotkeys.newTab) {
        const success = globalShortcut.register(settings.hotkeys.newTab, () => {
          this.onHotkeyPressed('newTab', settings.hotkeys.newTab);
        });

        if (success) {
          this.registeredHotkeys.push(settings.hotkeys.newTab);
          this.logger.debug(`Registered hotkey: ${settings.hotkeys.newTab}`);
        } else {
          this.logger.warn(`Failed to register hotkey: ${settings.hotkeys.newTab}`);
        }
      }

      // Register refresh hotkey
      if (settings.hotkeys.refresh) {
        const success = globalShortcut.register(settings.hotkeys.refresh, () => {
          this.onHotkeyPressed('refresh', settings.hotkeys.refresh);
        });

        if (success) {
          this.registeredHotkeys.push(settings.hotkeys.refresh);
          this.logger.debug(`Registered hotkey: ${settings.hotkeys.refresh}`);
        } else {
          this.logger.warn(`Failed to register hotkey: ${settings.hotkeys.refresh}`);
        }
      }

      this.logger.info(`Registered ${this.registeredHotkeys.length} hotkeys`);

    } catch (error) {
      this.logger.error('Error registering hotkeys:', error);
      throw error;
    }
  }

  private unregisterHotkeys(): void {
    try {
      for (const hotkey of this.registeredHotkeys) {
        globalShortcut.unregister(hotkey);
        this.logger.debug(`Unregistered hotkey: ${hotkey}`);
      }

      this.registeredHotkeys = [];
      this.logger.info('All hotkeys unregistered');

    } catch (error) {
      this.logger.error('Error unregistering hotkeys:', error);
    }
  }

  private startMouseTracking(): void {
    const settings = this.configurationService.getSettings();
    
    if (!settings.ui.autoHide) {
      this.logger.debug('Auto-hide disabled, skipping mouse tracking');
      return;
    }

    // Check mouse position every 100ms
    this.mouseCheckInterval = setInterval(() => {
      this.checkMousePosition();
    }, 100);

    this.logger.debug('Mouse tracking started');
  }

  private stopMouseTracking(): void {
    if (this.mouseCheckInterval) {
      clearInterval(this.mouseCheckInterval);
      delete (this as any).mouseCheckInterval;
    }

    if (this.activationTimer) {
      clearTimeout(this.activationTimer);
      delete (this as any).activationTimer;
    }

    this.isInActivationZone = false;
    this.logger.debug('Mouse tracking stopped');
  }

  private checkMousePosition(): void {
    try {
      const currentTime = Date.now();
      
      // Debounce mouse checks to prevent rapid toggling
      if (currentTime - this.lastMouseCheckTime < this.mouseCheckDebounceMs) {
        return;
      }
      
      this.lastMouseCheckTime = currentTime;

      const currentPosition = screen.getCursorScreenPoint();

      // Only check if mouse has moved significantly (reduce noise)
      const mouseMoveThreshold = 2; // pixels
      const xDiff = Math.abs(currentPosition.x - this.lastMousePosition.x);
      const yDiff = Math.abs(currentPosition.y - this.lastMousePosition.y);
      
      if (xDiff < mouseMoveThreshold && yDiff < mouseMoveThreshold) {
        return;
      }

      this.lastMousePosition = currentPosition;

      const settings = this.configurationService.getSettings();
      const display = screen.getPrimaryDisplay();
      const workArea = display.workArea;

      const isInActivationZone = this.isMouseInActivationZone(currentPosition, workArea, settings.ui.panelPosition);

      // Check if mouse is over panel (if panel bounds are available)
      let isOverPanel = false;
      if (this.panelBounds) {
        isOverPanel = currentPosition.x >= this.panelBounds.x &&
                     currentPosition.x <= this.panelBounds.x + this.panelBounds.width &&
                     currentPosition.y >= this.panelBounds.y &&
                     currentPosition.y <= this.panelBounds.y + this.panelBounds.height;
      }

      // Update panel hover state
      if (isOverPanel !== this.isMouseOverPanel) {
        this.isMouseOverPanel = isOverPanel;
        this.logger.debug(`Mouse over panel: ${isOverPanel}`);

        // **IMMEDIATE RESPONSIVENESS: Hide panel immediately when mouse leaves, regardless of other conditions**
        if (!isOverPanel && this.isPreviewMode) {
          this.logger.debug('Hiding panel - mouse left panel area (immediate response)');
          this.isPreviewMode = false;
          this.emit('deactivate', new ActivationEventArgs(ActivationSource.MouseHover, { preview: true }));
        }
      }

      // Handle activation zone changes
      if (isInActivationZone && !this.isInActivationZone) {
        this.onMouseEnterActivationZone();
      } else if (!isInActivationZone && this.isInActivationZone) {
        this.onMouseLeaveActivationZone();
      }

    } catch (error) {
      this.logger.error('Error checking mouse position:', error);
    }
  }

  private isMouseInActivationZone(
    mousePos: { x: number; y: number },
    workArea: { x: number; y: number; width: number; height: number },
    panelPosition: PanelPosition
  ): boolean {
    const settings = this.configurationService.getSettings();
    const edgeConfig = settings.ui.edgeActivation;

    // Fallback to legacy behavior if edgeActivation is not configured
    if (!edgeConfig) {
      const activationZoneWidth = 4; // 4 pixel activation zone
      return mousePos.x <= (workArea.x + activationZoneWidth) &&
             mousePos.y >= workArea.y &&
             mousePos.y <= (workArea.y + workArea.height);
    }

    // Determine which edge to check based on panel position
    let activeEdge = edgeConfig.left;
    if (panelPosition === PanelPosition.Right) {
      activeEdge = edgeConfig.right;
    }

    // If the edge is not configured or not enabled, fallback to legacy behavior
    if (!activeEdge || !activeEdge.enabled) {
      const activationZoneWidth = 4; // 4 pixel activation zone
      return mousePos.x <= (workArea.x + activationZoneWidth) &&
             mousePos.y >= workArea.y &&
             mousePos.y <= (workArea.y + workArea.height);
    }

    // Calculate activation zone bounds
    const zoneWidth = activeEdge.width;
    const zoneSize = activeEdge.size; // Percentage of screen edge
    const zoneOffset = activeEdge.offset;

    let zoneBounds: { x: number; y: number; width: number; height: number };

    if (panelPosition === PanelPosition.Left) {
      // Left edge activation
      const zoneHeight = Math.floor((workArea.height * zoneSize) / 100);
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      zoneBounds = {
        x: workArea.x,
        y: zoneY,
        width: zoneWidth,
        height: activeEdge.position === 'full' ? workArea.height : zoneHeight
      };
    } else {
      // Right edge activation
      const zoneHeight = Math.floor((workArea.height * zoneSize) / 100);
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      zoneBounds = {
        x: workArea.x + workArea.width - zoneWidth,
        y: zoneY,
        width: zoneWidth,
        height: activeEdge.position === 'full' ? workArea.height : zoneHeight
      };
    }

    // Check if mouse is within the activation zone
    return mousePos.x >= zoneBounds.x &&
           mousePos.x <= zoneBounds.x + zoneBounds.width &&
           mousePos.y >= zoneBounds.y &&
           mousePos.y <= zoneBounds.y + zoneBounds.height;
  }

  private onMouseEnterActivationZone(): void {
    this.isInActivationZone = true;

    const settings = this.configurationService.getSettings();
    const delay = settings.ui.activationDelay;

    this.logger.debug(`Mouse entered activation zone, delay: ${delay}ms`);

    // Show visual indicator
    this.showActivationIndicator();

    // Clear any existing timer
    if (this.activationTimer) {
      clearTimeout(this.activationTimer);
    }

    // Set preview timer (shorter delay for preview)
    this.activationTimer = setTimeout(() => {
      if (this.isInActivationZone && !this.isPanelPinned) {
        this.logger.debug('Showing panel preview via mouse hover');
        this.isPreviewMode = true;
        this.emit('activate', new ActivationEventArgs(ActivationSource.MouseHover, { preview: true }));
      }
    }, Math.min(delay, 200)); // Max 200ms for preview
  }

  private onMouseLeaveActivationZone(): void {
    this.isInActivationZone = false;

    this.logger.debug('Mouse left activation zone');

    // Hide visual indicator
    this.hideActivationIndicator();

    // Clear activation timer
    if (this.activationTimer) {
      clearTimeout(this.activationTimer);
      delete (this as any).activationTimer;
    }

    // **IMMEDIATE RESPONSIVENESS: Hide if in preview mode and not over panel**
    if (this.isPreviewMode && !this.isMouseOverPanel) {
      this.logger.debug('Hiding panel - mouse left activation zone and not over panel');
      this.isPreviewMode = false;
      this.emit('deactivate', new ActivationEventArgs(ActivationSource.MouseHover, { preview: true }));
    } else if (this.isPreviewMode && this.isMouseOverPanel) {
      this.logger.debug('Keeping panel open - mouse is over panel area');
    }
  }

  private onHotkeyPressed(action: string, hotkey: string): void {
    this.logger.debug(`Hotkey pressed: ${hotkey} (${action})`);

    switch (action) {
      case 'togglePanel':
        this.emit('activate', new ActivationEventArgs(ActivationSource.Hotkey, { action: 'toggle' }));
        break;
      
      case 'newTab':
        this.emit('activate', new ActivationEventArgs(ActivationSource.Hotkey, { action: 'newTab' }));
        break;
      
      case 'refresh':
        this.emit('activate', new ActivationEventArgs(ActivationSource.Hotkey, { action: 'refresh' }));
        break;
      
      default:
        this.logger.warn(`Unknown hotkey action: ${action}`);
    }
  }

  // Public methods for panel state management
  setPanelPinned(pinned: boolean): void {
    this.isPanelPinned = pinned;
    this.isPreviewMode = false;
    this.logger.debug(`Panel pinned state changed: ${pinned}`);
  }

  setPanelBounds(bounds: { x: number; y: number; width: number; height: number }): void {
    this.panelBounds = bounds;
    this.logger.debug(`Panel bounds updated:`, bounds);
  }

  activatePanel(): void {
    if (this.isInActivationZone) {
      this.logger.debug('Activating panel via click');
      this.isPanelPinned = true;
      this.isPreviewMode = false;
      this.emit('activate', new ActivationEventArgs(ActivationSource.MouseClick, { pinned: true }));
    }
  }

  // **NEW: Public getters for mouse state (for grace period logic)**
  getIsMouseOverPanel(): boolean {
    return this.isMouseOverPanel;
  }

  getIsInActivationZone(): boolean {
    return this.isInActivationZone;
  }

  getIsPreviewMode(): boolean {
    return this.isPreviewMode;
  }

  /**
   * Check if panel should be hidden based on current mouse state
   * Used by grace period logic to determine if panel should hide when grace period ends
   */
  shouldHidePanel(): boolean {
    return this.isPreviewMode && !this.isPanelPinned && !this.isMouseOverPanel && !this.isInActivationZone;
  }

  private showActivationIndicator(): void {
    if (this.activationIndicator) {
      return; // Already showing
    }

    const display = screen.getPrimaryDisplay();
    const workArea = display.workArea;
    const settings = this.configurationService.getSettings();
    const edgeConfig = settings.ui.edgeActivation;

    // Fallback to legacy behavior if edgeActivation is not configured
    if (!edgeConfig) {
      this.activationIndicator = new BrowserWindow({
        x: workArea.x,
        y: workArea.y,
        width: 4,
        height: workArea.height,
        frame: false,
        transparent: true,
        alwaysOnTop: true,
        skipTaskbar: true,
        resizable: false,
        movable: false,
        minimizable: false,
        maximizable: false,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      });

      this.activationIndicator.loadURL(`data:text/html,
        <html>
          <body style="margin:0;padding:0;background:linear-gradient(90deg, #0078d4 0%, transparent 100%);cursor:pointer;"></body>
        </html>
      `);

      this.activationIndicator.show();
      this.logger.debug('Legacy activation indicator shown');
      return;
    }

    // Determine which edge to show based on panel position
    let activeEdge = edgeConfig.left;
    if (settings.ui.panelPosition === PanelPosition.Right) {
      activeEdge = edgeConfig.right;
    }

    if (!activeEdge || !activeEdge.enabled) {
      return; // Don't show indicator if edge is disabled or not configured
    }

    // Calculate indicator bounds
    const zoneWidth = activeEdge.width;
    const zoneSize = activeEdge.size;
    const zoneOffset = activeEdge.offset;
    const zoneHeight = activeEdge.position === 'full' ? workArea.height : Math.floor((workArea.height * zoneSize) / 100);

    let indicatorBounds: { x: number; y: number; width: number; height: number };

    if (settings.ui.panelPosition === PanelPosition.Left) {
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      indicatorBounds = {
        x: workArea.x,
        y: zoneY,
        width: zoneWidth,
        height: zoneHeight
      };
    } else {
      let zoneY = workArea.y;

      switch (activeEdge.position) {
        case 'top':
          zoneY = workArea.y + zoneOffset;
          break;
        case 'middle':
          zoneY = workArea.y + Math.floor((workArea.height - zoneHeight) / 2) + zoneOffset;
          break;
        case 'bottom':
          zoneY = workArea.y + workArea.height - zoneHeight - zoneOffset;
          break;
        case 'full':
          zoneY = workArea.y;
          break;
      }

      indicatorBounds = {
        x: workArea.x + workArea.width - zoneWidth,
        y: zoneY,
        width: zoneWidth,
        height: zoneHeight
      };
    }

    this.activationIndicator = new BrowserWindow({
      x: indicatorBounds.x,
      y: indicatorBounds.y,
      width: indicatorBounds.width,
      height: indicatorBounds.height,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      movable: false,
      minimizable: false,
      maximizable: false,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    // Load simple HTML with colored background and click detection
    const gradient = settings.ui.panelPosition === PanelPosition.Left
      ? 'linear-gradient(90deg, #0078d4 0%, transparent 100%)'
      : 'linear-gradient(270deg, #0078d4 0%, transparent 100%)';

    this.activationIndicator.loadURL(`data:text/html,
      <html>
        <body style="margin:0;padding:0;background:${gradient};cursor:pointer;"></body>
        <script>
          document.body.addEventListener('click', () => {
            require('electron').ipcRenderer.send('activation-click');
          });
        </script>
      </html>
    `);

    this.activationIndicator.show();
    this.logger.debug(`Activation indicator shown at (${indicatorBounds.x}, ${indicatorBounds.y}) ${indicatorBounds.width}x${indicatorBounds.height}`);
  }

  private hideActivationIndicator(): void {
    if (this.activationIndicator) {
      this.activationIndicator.close();
      delete (this as any).activationIndicator;
      this.logger.debug('Activation indicator hidden');
    }
  }
}
